# 相较于同类型产品的不同之处

## 没有版本隔离：基于元数据的实例架构

版本隔离已成为过去式，一个版本就是一个实例，使用可迁移元数据描述一个游玩体验，而非一组特定文件。而元数据描述文件可以通过网络发送给其他用户，使其构建出一个一模一样的游戏体验（
*注*完整的游戏体验还包含附带的配置文件，虽然元数据文件是便携式的，但还是建议使用导出为整合包的方式对游戏进行分发和版本控制）

元数据描述游戏体验也带来另一个好处 ----
随时编辑和升级：可以在任何时候对一个实例包含的模组元数据进行增删改查以及升级或倒退为特定版本，或是修改游戏的加载器，一切都会在一键部署之后应用，从冰冷冷的一长串字符串变成活生生的、可运行的游戏文件。

上面有提到**便携式/可迁移**吗？是的，对于整合包开发者，你可以使用 VCS 版本控制软件（例如
Git）来共同协作/版本化控制你正在开发中的整合包！任何第二台电脑都可以从元数据文件中还原出来相同的游戏体验。

## 没有运行时检测：自动配置运行时

绝不扫盘，运行时会在部署实例时自动决定版本和二进制文件，全程无需用户操心。当然也可以手动指定。

## 没有混乱的资源管理：组件之间通过组合形成游戏实体

所有只读文件通过资源集成实现共享，不该共享的配置和存档等则各自独立。当多个实例都使用同一个材质包的同一个版本时，硬盘里只会存在一份材质包文件，通过软链接分派给这些实例。当其中一个实例使用该材质包的不同版本时，其他实例不受影响 ----
我说过了，他们是独立的。

## 会检查文件完整性：是的，会检查游戏完整性

下载安装了一个整合包结果运行时发现三百个模组只下载了一百多个？不会，部署引擎在对元数据进行构建出游戏实体文件时会对近万个文件进行完整性校验以确保不会漏下载文件、下载错文件、损坏文件。
