<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Thickness x:Key="DrawerContentMargin">18</Thickness>
    <BoxShadows x:Key="DrawerBoxShadow">0 0 4 0 #3F000000</BoxShadows>

    <ControlTheme x:Key="{x:Type local:Sidebar}" TargetType="local:Sidebar">
        <Setter Property="MinWidth" Value="256" />
        <Setter Property="Background" Value="{StaticResource FlyoutBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Padding" Value="{StaticResource DrawerContentMargin}" />
        <Setter Property="Margin" Value="6" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border ClipToBounds="True" Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        CornerRadius="{TemplateBinding CornerRadius}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}" Margin="4"
                        BoxShadow="{StaticResource DrawerBoxShadow}">
                    <ContentPresenter Name="PART_ContentPresenter"
                                      Padding="{TemplateBinding Padding}" Foreground="{TemplateBinding Foreground}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
