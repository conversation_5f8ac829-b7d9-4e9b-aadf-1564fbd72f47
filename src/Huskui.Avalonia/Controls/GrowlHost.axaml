<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:GrowlHost}" TargetType="local:GrowlHost">
        <Setter Property="Template">
            <ControlTemplate>
                <Border>
                    <ItemsControl
                        ItemsSource="{Binding Items,Mode=OneTime,RelativeSource={RelativeSource TemplatedParent}}"
                        ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
