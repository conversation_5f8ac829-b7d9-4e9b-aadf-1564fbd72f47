<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:LazyContainer}" TargetType="local:LazyContainer">
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <local:SkeletonContainer CornerRadius="{TemplateBinding CornerRadius}">
                    <local:SkeletonContainer.IsLoading>
                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                            <Binding Path="IsBad" Converter="{x:Static BoolConverters.Not}"
                                     RelativeSource="{RelativeSource TemplatedParent}">
                            </Binding>
                            <Binding Path="Source.IsInProgress" RelativeSource="{RelativeSource TemplatedParent}"
                                     FallbackValue="{x:False}" />
                        </MultiBinding>
                    </local:SkeletonContainer.IsLoading>
                    <ContentPresenter Name="{x:Static local:LazyContainer.PART_ContentPresenter}" />
                </local:SkeletonContainer>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
