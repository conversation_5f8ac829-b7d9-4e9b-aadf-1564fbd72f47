<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      x:Class="Huskui.Gallery.Views.InfoBarsPage">
    <StackPanel Spacing="32">

        <controls:ExampleContainer Title="Basic Info Bars"
                                   Description="Info bars with different severity levels">
            <StackPanel Spacing="16">
                <husk:InfoBar Header="Information" Content="This is an informational message." />
                <husk:InfoBar Header="Success" Content="Operation completed successfully." Classes="Success" />
                <husk:InfoBar Header="Warning" Content="Please review your settings." Classes="Warning" />
                <husk:InfoBar Header="Error" Content="An error occurred during processing." Classes="Danger" />
            </StackPanel>
        </controls:ExampleContainer>
    </StackPanel>

</controls:ControlPage>
