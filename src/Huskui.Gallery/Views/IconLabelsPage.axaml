<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                      x:Class="Huskui.Gallery.Views.IconLabelsPage">
    <StackPanel Spacing="32">

        <controls:ExampleContainer Title="Basic Icon Labels"
                                   Description="Icon and text combinations with FluentIcons integration">
            <StackPanel Spacing="16">
                <StackPanel Orientation="Horizontal" Spacing="16">
                    <husk:IconLabel Icon="Home" Text="Home" />
                    <husk:IconLabel Icon="Settings" Text="Settings" />
                    <husk:IconLabel Icon="Person" Text="Profile" />
                    <husk:IconLabel Icon="Mail" Text="Messages" />
                </StackPanel>
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Different Sizes"
                                   Description="Icon labels with various font sizes">
            <StackPanel Spacing="16">
                <husk:IconLabel Icon="Star" Text="Small" FontSize="12" />
                <husk:IconLabel Icon="Star" Text="Medium" FontSize="16" />
                <husk:IconLabel Icon="Star" Text="Large" FontSize="20" />
                <husk:IconLabel Icon="Star" Text="Extra Large" FontSize="24" />
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Custom Spacing"
                                   Description="Adjusting the space between icon and text">
            <StackPanel Spacing="16">
                <husk:IconLabel Icon="Heart" Text="No Spacing" Spacing="0" />
                <husk:IconLabel Icon="Heart" Text="Small Spacing" Spacing="4" />
                <husk:IconLabel Icon="Heart" Text="Medium Spacing" Spacing="8" />
                <husk:IconLabel Icon="Heart" Text="Large Spacing" Spacing="16" />
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Icon Variants"
                                   Description="Different icon styles using IconVariant property">
            <StackPanel Spacing="16">
                <husk:IconLabel Icon="Save" Text="Regular" Variant="Regular" />
                <husk:IconLabel Icon="Save" Text="Filled" Variant="Filled" />
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Styled Icon Labels"
                                   Description="Icon labels with custom colors and weights">
            <StackPanel Spacing="16">
                <husk:IconLabel Icon="CheckmarkCircle" Text="Success"
                                Foreground="{StaticResource ControlSuccessForegroundBrush}"
                                FontWeight="Bold" />
                <husk:IconLabel Icon="ErrorCircle" Text="Error"
                                Foreground="{StaticResource ControlDangerForegroundBrush}"
                                FontWeight="Bold" />
                <husk:IconLabel Icon="Warning" Text="Warning"
                                Foreground="{StaticResource ControlWarningForegroundBrush}"
                                FontWeight="Bold" />
                <husk:IconLabel Icon="Info" Text="Information"
                                Foreground="{StaticResource ControlAccentForegroundBrush}"
                                FontWeight="Bold" />
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Navigation Menu Example"
                                   Description="Common use case for navigation menus">
            <StackPanel Spacing="8">
                <husk:IconLabel Icon="Home" Text="Dashboard" FontSize="14" />
                <husk:IconLabel Icon="Document" Text="Documents" FontSize="14" />
                <husk:IconLabel Icon="Image" Text="Gallery" FontSize="14" />
                <husk:IconLabel Icon="Settings" Text="Settings" FontSize="14" />
                <husk:IconLabel Icon="QuestionCircle" Text="Help" FontSize="14" />
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Button Integration"
                                   Description="Using IconLabel inside buttons">
            <StackPanel Spacing="12">
                <Button Classes="Primary">
                    <husk:IconLabel Icon="Add" Text="Create New" Foreground="White" />
                </Button>
                <Button Classes="Success">
                    <husk:IconLabel Icon="Save" Text="Save Changes" Foreground="White" />
                </Button>
                <Button Classes="Danger">
                    <husk:IconLabel Icon="Delete" Text="Delete Item" Foreground="White" />
                </Button>
            </StackPanel>
        </controls:ExampleContainer>

    </StackPanel>
</controls:ControlPage>
