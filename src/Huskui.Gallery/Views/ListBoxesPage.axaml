<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                      x:Class="Huskui.Gallery.Views.ListBoxesPage">
    <StackPanel Spacing="32">

        <controls:ExampleContainer Title="Basic ListBoxes"
                                   Description="Standard listbox controls with different configurations">
            <controls:ExampleContainer.Controls>
                <StackPanel Spacing="12">
                    <TextBlock Text="Selection Controls" FontWeight="Bold" FontSize="14" />
                    <Button Content="Clear Selection" Click="OnClearSelectionClick" />
                    <Button Content="Reset to Defaults" Click="OnResetSelectionClick" />
                    <TextBlock Text="Clear all selections or reset to default values."
                               TextWrapping="Wrap"
                               FontSize="12"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </controls:ExampleContainer.Controls>

            <StackPanel Spacing="16">
                <ListBox x:Name="BasicListBox" Width="200" Height="200" BorderThickness="1" BorderBrush="Black">
                    <ListBoxItem>Item 1</ListBoxItem>
                    <ListBoxItem>Item 2</ListBoxItem>
                    <ListBoxItem>Item 3</ListBoxItem>
                    <ListBoxItem>Item 4</ListBoxItem>
                </ListBox>
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="MultiSelect ListBoxes"
                                   Description="Listboxes with multi-select enabled">
            <StackPanel Spacing="16">
                <ListBox x:Name="MultiSelectListBox" Width="200" Height="200" BorderThickness="1" BorderBrush="Black"
                         SelectionMode="Multiple">
                    <ListBoxItem>Item 1</ListBoxItem>
                    <ListBoxItem>Item 2</ListBoxItem>
                    <ListBoxItem>Item 3</ListBoxItem>
                    <ListBoxItem>Item 4</ListBoxItem>
                </ListBox>
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Custom ListBoxes"
                                   Description="Listboxes with custom item templates">
            <StackPanel Spacing="16">
                <ListBox x:Name="CustomListBox" Width="200" Height="200" BorderThickness="1" BorderBrush="Black">
                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <TextBlock Text="Item 1" />
                            <TextBlock Text="Description 1" FontSize="12"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <TextBlock Text="Item 2" />
                            <TextBlock Text="Description 2" FontSize="12"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <TextBlock Text="Item 3" />
                            <TextBlock Text="Description 3" FontSize="12"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                    </ListBoxItem>
                </ListBox>
            </StackPanel>
        </controls:ExampleContainer>

    </StackPanel>
</controls:ControlPage>
