<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <LangVersion>preview</LangVersion>
        <AssemblyTitle>Huskui Gallery</AssemblyTitle>
        <AssemblyDescription>A comprehensive gallery showcasing Huskui.Avalonia controls</AssemblyDescription>
        <ApplicationIcon>Assets\Icon.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.5"/>
        <PackageReference Include="Avalonia.Desktop" Version="11.3.5"/>
        <PackageReference Include="Avalonia.Diagnostics" Version="11.3.5">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0"/>
        <PackageReference Include="FluentIcons.Avalonia" Version="1.1.308"/>
        <PackageReference Include="HotAvalonia" Version="3.0.0">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8"/>
        <PackageReference Include="DynamicData" Version="9.4.1"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Huskui.Avalonia\Huskui.Avalonia.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <ItemGroup>
      <Compile Update="MainWindow.axaml.cs">
        <DependentUpon>MainWindow.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\HomePage.axaml.cs">
        <DependentUpon>HomePage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\DialogsPage.axaml.cs">
        <DependentUpon>DialogsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\FlyoutsPage.axaml.cs">
        <DependentUpon>FlyoutsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\GrowlsPage.axaml.cs">
        <DependentUpon>GrowlsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ModalsPage.axaml.cs">
        <DependentUpon>ModalsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ToastsPage.axaml.cs">
        <DependentUpon>ToastsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ToolTipsPage.axaml.cs">
        <DependentUpon>ToolTipsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\FlexWrapPanelsPage.axaml.cs">
        <DependentUpon>FlexWrapPanelsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\CheckBoxesPage.axaml.cs">
        <DependentUpon>CheckBoxesPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ComboBoxesPage.axaml.cs">
        <DependentUpon>ComboBoxesPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\TextBoxesPage.axaml.cs">
        <DependentUpon>TextBoxesPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ToggleSwitchesPage.axaml.cs">
        <DependentUpon>ToggleSwitchesPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ButtonsPage.axaml.cs">
        <DependentUpon>ButtonsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\DividersPage.axaml.cs">
        <DependentUpon>DividersPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\DropDownButtonsPage.axaml.cs">
        <DependentUpon>DropDownButtonsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\HighlightBlocksPage.axaml.cs">
        <DependentUpon>HighlightBlocksPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\HyperlinkButtonsPage.axaml.cs">
        <DependentUpon>HyperlinkButtonsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\IconLabelsPage.axaml.cs">
        <DependentUpon>IconLabelsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\InfoBarsPage.axaml.cs">
        <DependentUpon>InfoBarsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\RadioButtonsPage.axaml.cs">
        <DependentUpon>RadioButtonsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\TagsPage.axaml.cs">
        <DependentUpon>TagsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\BusyContainersPage.axaml.cs">
        <DependentUpon>BusyContainersPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\CardsPage.axaml.cs">
        <DependentUpon>CardsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\ListBoxesPage.axaml.cs">
        <DependentUpon>ListBoxesPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\SkeletonContainersPage.axaml.cs">
        <DependentUpon>SkeletonContainersPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\TabControlsPage.axaml.cs">
        <DependentUpon>TabControlsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\TabStripsPage.axaml.cs">
        <DependentUpon>TabStripsPage.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
    </ItemGroup>
</Project>
