<husk:AppWindow xmlns="https://github.com/avaloniaui"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                xmlns:vm="clr-namespace:Huskui.Gallery.ViewModels"
                xmlns:models="clr-namespace:Huskui.Gallery.Models"
                xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
                x:Class="Huskui.Gallery.MainWindow"
                x:DataType="vm:MainWindowViewModel"
                Title="Huskui Gallery"
                MinWidth="800" MinHeight="600"
                ExtendClientAreaTitleBarHeightHint="44"
                TransparencyLevelHint="Mica, None">

    <husk:AppWindow.Resources>
        <!-- Shared Gallery Item Template -->
        <DataTemplate x:Key="GalleryItemTemplate" x:DataType="models:GalleryItem">
            <Grid ColumnDefinitions="Auto,*,Auto" Margin="4">
                <fi:SymbolIcon Grid.Column="0"
                               Symbol="{Binding Icon}"
                               FontSize="{StaticResource MediumFontSize}"
                               Margin="0,0,12,0" />
                <StackPanel Grid.Column="1">
                    <TextBlock Text="{Binding Title}"
                               FontWeight="{StaticResource ControlStrongFontWeight}" />
                    <TextBlock Text="{Binding Description}"
                               FontSize="{StaticResource SmallFontSize}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                               TextTrimming="CharacterEllipsis" />
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="4">
                    <Border Classes="NewBadge" IsVisible="{Binding IsNew}">
                        <TextBlock Text="NEW" Classes="BadgeText" />
                    </Border>
                    <Border Classes="UpdatedBadge" IsVisible="{Binding IsUpdated}">
                        <TextBlock Text="UPD" Classes="BadgeText" />
                    </Border>
                </StackPanel>
            </Grid>
        </DataTemplate>
    </husk:AppWindow.Resources>

    <Grid ColumnDefinitions="320,*">
        <!-- Sidebar -->
        <Border Grid.Column="0"
                Background="{StaticResource LayerBackgroundBrush}"
                BorderBrush="{StaticResource ControlBorderBrush}"
                BorderThickness="0,0,1,0">
            <Grid RowDefinitions="Auto,Auto,*,Auto">
                <!-- Header -->
                <StackPanel Grid.Row="0" Margin="16" Spacing="16">
                    <!-- Title -->
                    <StackPanel Spacing="4">
                        <TextBlock Text="Huskui Gallery"
                                   FontSize="{StaticResource LargeFontSize}"
                                   FontWeight="{StaticResource ControlStrongFontWeight}" />
                        <TextBlock Text="Control Library Showcase"
                                   FontSize="{StaticResource SmallFontSize}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>

                    <!-- Search -->
                    <TextBox Text="{Binding SearchText}"
                             Classes="SearchBox"
                             Watermark="Search controls...">
                        <TextBox.InnerLeftContent>
                            <fi:SymbolIcon Symbol="Search" FontSize="{StaticResource SmallFontSize}" Margin="12,0,0,0" />
                        </TextBox.InnerLeftContent>
                    </TextBox>
                </StackPanel>

                <!-- Navigation Buttons -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="16,0" Spacing="8">
                    <Button Command="{Binding NavigateHomeCommand}"
                            Theme="{StaticResource GhostButtonTheme}"
                            Classes="Small"
                            ToolTip.Tip="Home">
                        <fi:SymbolIcon Symbol="Home" FontSize="{StaticResource SmallFontSize}" />
                    </Button>
                    <Button Command="{Binding GoBackCommand}"
                            Theme="{StaticResource GhostButtonTheme}"
                            Classes="Small"
                            IsEnabled="{Binding NavigationService.CanGoBack}"
                            ToolTip.Tip="Back">
                        <fi:SymbolIcon Symbol="ArrowLeft" FontSize="{StaticResource SmallFontSize}" />
                    </Button>
                    <Button Command="{Binding GoForwardCommand}"
                            Theme="{StaticResource GhostButtonTheme}"
                            Classes="Small"
                            IsEnabled="{Binding NavigationService.CanGoForward}"
                            ToolTip.Tip="Forward">
                        <fi:SymbolIcon Symbol="ArrowRight" FontSize="{StaticResource SmallFontSize}" />
                    </Button>
                </StackPanel>

                <!-- Navigation Content -->
                <ScrollViewer Grid.Row="2">
                    <StackPanel Margin="8,16,8,8">
                        <!-- Search Results (Flat List) -->
                        <StackPanel IsVisible="{Binding IsSearchActive}">
                            <TextBlock Text="Search Results"
                                       Classes="CategoryHeader" />
                            <ListBox ItemsSource="{Binding SearchResults}"
                                     SelectedItem="{Binding SelectedItem}"
                                     Classes="NavigationList"
                                     ItemTemplate="{StaticResource GalleryItemTemplate}" />
                        </StackPanel>

                        <!-- Category Groups (Grouped List) -->
                        <ItemsControl ItemsSource="{Binding CategoryGroups}"
                                      IsVisible="{Binding !IsSearchActive}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate x:DataType="vm:CategoryGroupViewModel">
                                    <StackPanel IsVisible="{Binding IsVisible}">
                                        <TextBlock Text="{Binding Name}"
                                                   Classes="CategoryHeader" />
                                        <ListBox ItemsSource="{Binding FilteredItems}"
                                                 SelectedItem="{Binding SelectedItem}"
                                                 Classes="NavigationList"
                                                 ItemTemplate="{StaticResource GalleryItemTemplate}" />
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>

                <!-- Footer -->
                <Border Grid.Row="3"
                        Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                        BorderBrush="{StaticResource ControlBorderBrush}"
                        BorderThickness="0,1,0,0"
                        Padding="16,12">
                    <Grid ColumnDefinitions="*,Auto,Auto">
                        <TextBlock Grid.Column="0"
                                   Text="Huskui.Avalonia"
                                   FontSize="{StaticResource SmallFontSize}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                   VerticalAlignment="Center" />
                        <Button Grid.Column="1"
                                Command="{Binding ToggleThemeCommand}"
                                Theme="{StaticResource GhostButtonTheme}"
                                Classes="Small"
                                ToolTip.Tip="Toggle Theme"
                                Margin="0,0,8,0">
                            <fi:SymbolIcon Symbol="WeatherMoon" FontSize="{StaticResource SmallFontSize}" />
                        </Button>
                        <Button Grid.Column="2"
                                Command="{Binding ToggleSettingsCommand}"
                                Theme="{StaticResource GhostButtonTheme}"
                                Classes="Small"
                                ToolTip.Tip="Settings">
                            <fi:SymbolIcon Symbol="Settings" FontSize="{StaticResource SmallFontSize}" />
                        </Button>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Column="1">
            <!-- Content Frame -->
            <TransitioningContentControl Name="ContentFrame" />

            <!-- Settings Overlay -->
            <Border Background="{StaticResource OverlaySolidBackgroundBrush}"
                    BorderBrush="{StaticResource ControlBorderBrush}"
                    BorderThickness="1,0,0,0"
                    IsVisible="{Binding IsSettingsOpen}">
                <ScrollViewer Padding="32">
                    <StackPanel Spacing="32" MaxWidth="400">
                        <StackPanel Spacing="16">
                            <TextBlock Text="Settings"
                                       FontSize="{StaticResource LargeFontSize}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock Text="Customize the appearance and behavior of the gallery"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       TextWrapping="Wrap" />
                        </StackPanel>

                        <controls:ThemeSelector ThemeService="{Binding ThemeService}" />

                        <Button Content="Close"
                                Command="{Binding ToggleSettingsCommand}"
                                HorizontalAlignment="Right" />
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>

</husk:AppWindow>
